
## The updated code includes several improvements and new features, which are listed below:

* Command-line arguments: The argparse module is used to define and parse command-line arguments. This allows the user to specify the URL and the maximum depth to follow links as arguments when running the script.

* Multithreading: The ThreadPoolExecutor class is used to perform the HTML parsing and link extraction in parallel. This can speed up the process when parsing large websites.

* Error handling: The code handles errors more gracefully by catching and displaying exceptions that may occur during the request and parsing processes.

* Code structure: The code has a cleaner and more modular structure, with functions and classes separated into their own sections. The code is also better commented, making it easier to understand and modify.

* Overall, the updated code is a more robust and flexible version, with better error handling, command-line options, and multithreading support. It should be easier to use and extend, and more reliable when dealing with large websites or unexpected errors.

* I have made several changes to the repository including renaming the file and changing the script.py to link_grabber.py.

* Readme.md contains information on how users can Install and Run the script.

* The information that github users can contribute has been written which you can read.

--------------
- Credit: [jeturgavli](https://github.com/jeturgavli)
- Last Edited on: 06-05-2023



