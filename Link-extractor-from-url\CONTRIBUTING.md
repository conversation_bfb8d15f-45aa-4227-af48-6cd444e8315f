Welcome to the Contribution Guide and thank you for considering contributing. Contributions are welcome so that we can make this program even more accessible to users.

# 🤝 How to Contribute:
* Step 1: Fork this repository.

* Step 2: Clone the repository in your local machine.

* Step 3: Create a new branch

* Step 4: Create a new Markdown File. Your file name should be your GitHub username .

```
- File name should be username.md
```
* Step 5: What have you changed in the file? Write its detailed information in the username.md file.

* Step 6: At the end of the file add the following content with apporopriate changes.
  - Add line break using this command `------`
  - Credit: `[username](https://github.com/username)`
  - Last Edited on: Date when you edited this file. The date must be in DD/MM/YYYY format

<br>

* Step 8: Save the file in contributing Folder and commit your changes with the proper message. Your message must contain your username.

```
- Example Message : Code Changes by username: <username>
```


* Step 9: Push your commit to GitHub.

* Step 10: Submit a Pull Request.

* Step 11: All Done.

- After checking your changes you will be on the leaderboard of our Code Contributors. 😎🤞